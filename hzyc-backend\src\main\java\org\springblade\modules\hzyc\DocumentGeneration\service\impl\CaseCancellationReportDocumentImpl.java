package org.springblade.modules.hzyc.DocumentGeneration.service.impl;

import cn.hutool.core.util.StrUtil;
import org.springblade.modules.hzyc.DocumentGeneration.service.DocumentGenerator;
import org.springblade.modules.hzyc.DocumentGeneration.service.ICaseInfoService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springblade.modules.hzyc.document.service.IDocumentService;
import org.springblade.modules.hzyc.document.pojo.entity.DocumentEntity;
import org.springblade.core.tool.jackson.JsonUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import cn.hutool.json.JSONArray;

/**
 * 撤销立案报告表文档生成实现类
 *
 * <AUTHOR>
 */
@Service("caseCancellationReportDocument")
public class CaseCancellationReportDocumentImpl implements DocumentGenerator {

    @Value("${spring.profiles.active:dev}")
    private String activeProfile;

    @Autowired
    private IDocumentService documentService;
    @Autowired
    private ICaseInfoService icaseInfoService;

    @Override
    public List<Map<String, Object>> processData(Map<String, Object> rawData, String caseId, String mode, String fileId) {
        Map<String, Object> processedData = new HashMap<>(rawData);
        
        // 如果mode为update，从数据库读取已保存的数据
        if ("update".equals(mode) && caseId != null) {
            DocumentEntity existingDoc = documentService.getById(fileId);
            if (existingDoc != null && existingDoc.getDocumentContent() != null) {
                // 从JSON字符串解析为Map
                processedData = JsonUtil.readMap(existingDoc.getDocumentContent());
                return List.of(processedData);
            }
        }

        // 如果没有找到已保存的数据或mode不是update，根据环境判断数据类型
        System.out.println(caseId);
        // dev环境使用模拟数据(type=0)，prod环境使用真实数据(type=1)
        int dataType = "prod".equals(activeProfile) ? 1 : 0;
        processedData = getMockData(dataType, caseId);
        return List.of(processedData);
    }

    @Override
    public String getTemplateName() {
        return "撤销立案报告表.docx";
    }

    @Override
    public String getDocumentType() {
        return "CASE-CANCELLATION-REPORT";
    }

    public static Map<String, String> getReverseFieldMapping() {
        Map<String, String> mapping = new HashMap<>();

        mapping.put("AJBS", "case_uuid");
        mapping.put("WCZT", "completion_status");
        mapping.put("AQZY", "case_summary");
        mapping.put("CBRQ", "undertake_date");
        mapping.put("MCRKSJ", "mc_storage_time");
        mapping.put("SJMC", "city_name");
        mapping.put("LASJ", "register_time");
        mapping.put("SFYX", "is_active");
        mapping.put("SFYS1YS0F", "is_transfer");
        mapping.put("AY", "case_reason");
        mapping.put("SJSSDWYYTYSJQXCL", "data_owner_org");
        mapping.put("CBR", "undertaker");
        mapping.put("XGSJ", "modify_time");
        mapping.put("ZJHM", "id_number");
        mapping.put("CJR", "creator");
        mapping.put("DWJC", "org_shortname");
        mapping.put("XTCJSJCXBYDX", "sys_create_time");
        mapping.put("CJSJ", "create_time");
        mapping.put("ZJLX", "id_type");
        mapping.put("DZ", "address");
        mapping.put("CBRYJ", "undertaker_opinion");
        mapping.put("DSR", "party");
        mapping.put("CXBGBS", "cancellation_report_id");
        mapping.put("SJBM", "city_org_code");
        mapping.put("KZZD2", "ext2");
        mapping.put("BZ", "remark");
        mapping.put("KZZD3", "ext3");
        mapping.put("AFDD", "case_addr");
        mapping.put("CBRUUIDPJ", "undertaker_uuid_concat");
        mapping.put("XGR", "modifier");
        mapping.put("KZZD1", "ext1");
        mapping.put("AJBH", "case_code");
        mapping.put("WSH", "doc_no");
        mapping.put("XYWYBS", "tid");
        mapping.put("DWSXZ", "org_abbr");
        mapping.put("FWZXZDTBSJYFWZXSJG", "service_center_sync_flag");
        mapping.put("WSHQ", "full_doc_no");
        mapping.put("FWZXZDTBSJYFWZXSJS", "service_center_delete_flag");
        mapping.put("XTGXSJCXBYDX", "sys_modify_time");
        mapping.put("ND", "year");
        mapping.put("AFSJ", "case_date");
        mapping.put("AJLY", "case_source");
        mapping.put("SJSSBMYYTYSJQXCL", "data_owner_dept");

        return mapping;
    }

    /**
     * 获取模拟数据
     * @param type 数据类型：0-模拟数据(dev环境)，1-真实数据(prod环境)
     * @param caseId 案件ID
     */
    private Map<String, Object> getMockData(int type, String caseId) {
        Map<String, Object> mockData = new HashMap<>();
        
        if (type == 1) {
            Map<String, Object> query = new HashMap<>();
            query.put("AJBS", caseId);
            JSONArray array = icaseInfoService.getCaseCancellationReportDailyReport(query);

            // 如果array不为空，将第一条数据传给mockData
            if (array != null && array.size() > 0) {
                Map<String, Object> firstData = (Map<String, Object>) array.get(0);
                Map<String, Object> processData = new HashMap<>();
                Map<String, String> mapper = getReverseFieldMapping();
                if (firstData != null) {
                    // 处理数据
                    firstData.forEach((key, value) -> {
                        String newKey = mapper.get(key);
                        if (StrUtil.isBlank(newKey)) {
                            newKey = key;
                        }
                        processData.put(newKey, value);
                    });
                    return processData;
                }
            }
        }

        // 基础信息
        mockData.put("case_uuid", "6358d676d24647f1b824c1f362674299");
        mockData.put("completion_status", 1);
        mockData.put("case_summary", "当事人梁俊强未在当地烟草专卖批发企业进货案件，经调查核实，决定撤销立案。");
        mockData.put("undertake_date", "2025/3/18");
        mockData.put("mc_storage_time", "2025/6/11 1:03");
        mockData.put("city_name", "惠州市");
        mockData.put("register_time", "2025/3/18");
        mockData.put("is_active", 1);
        mockData.put("is_transfer", 0);
        mockData.put("case_reason", "未在当地烟草专卖批发企业进货");
        mockData.put("data_owner_org", "4413231030000000540");
        mockData.put("undertaker", "蔡秋宝");
        mockData.put("modify_time", "2025/6/10 17:15");
        mockData.put("id_number", "441322199203166034");
        mockData.put("creator", "蔡秋宝");
        mockData.put("org_shortname", "广东省博罗县烟草专卖局");
        mockData.put("sys_create_time", "2025/6/10 17:15");
        mockData.put("create_time", "2025/6/10 17:15");
        mockData.put("id_type", "居民身份证");
        mockData.put("address", "广东省博罗县龙溪街道长湖村合湖小组193号");
        mockData.put("undertaker_opinion", "经调查核实，当事人梁俊强经营的博罗县龙溪隆胜轩茶烟酒商行确实存在未在当地烟草专卖批发企业进货的违法行为，但考虑到案件情况复杂，建议撤销立案。");
        mockData.put("party", "梁俊强");
        mockData.put("cancellation_report_id", "CXBG2025001");
        mockData.put("city_org_code", "10441300");
        mockData.put("ext2", "");
        mockData.put("remark", "撤销立案报告");
        mockData.put("ext3", "");
        mockData.put("case_addr", "广东省博罗县龙溪街道宫庭村龙桥大道1239号");
        mockData.put("undertaker_uuid_concat", "4413231030000010669");
        mockData.put("modifier", "蔡秋宝");
        mockData.put("ext1", "");
        mockData.put("case_code", "博烟案﹝2025﹞第48号");
        mockData.put("doc_no", "博烟撤案﹝2025﹞第001号");
        mockData.put("tid", "");
        mockData.put("org_abbr", "博罗县烟草专卖局");
        mockData.put("service_center_sync_flag", "");
        mockData.put("full_doc_no", "博烟撤案﹝2025﹞第001号");
        mockData.put("service_center_delete_flag", 0);
        mockData.put("sys_modify_time", "2025/6/10 17:15");
        mockData.put("year", "2025");
        mockData.put("case_date", "2025/3/18");
        mockData.put("case_source", "群众举报");
        mockData.put("data_owner_dept", "4413231030000002829");

        return mockData;
    }
}
